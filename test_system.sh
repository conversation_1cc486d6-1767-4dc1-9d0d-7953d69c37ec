#!/usr/bin/env bash
# Test completo del sistema multi-edge
# Ejecutar desde cualquier edge después de la instalación

set -euo pipefail

echo "=== TEST SISTEMA MULTI-EDGE ==="
echo "Ejecutando desde: $(hostname -I | awk '{print $1}')"
echo "Fecha: $(date)"
echo ""

# Test 1: Servicios locales
echo "🔍 Test 1: Servicios activos"
systemctl is-active xray nginx fail2ban || echo "❌ Algún servicio inactivo"
echo "✅ Servicios verificados"
echo ""

# Test 2: Puertos escuchando
echo "🔍 Test 2: Puertos abiertos"
ss -lnpt | grep -E ":80|:443|:2052|:8880|:31210|:10000|:18081" || echo "⚠️  Algunos puertos no están escuchando"
echo "✅ Puertos verificados"
echo ""

# Test 3: Conexión al origin
echo "🔍 Test 3: Túnel al origin"
if curl -s --max-time 5 http://localhost:18081/ >/dev/null; then
  echo "✅ Túnel HTTP funciona"
else
  echo "❌ Túnel HTTP falla"
fi
echo ""

# Test 4: Certificado SSL
echo "🔍 Test 4: Certificado SSL"
DOMAIN="cdn.rgstreamy.site"
if openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} </dev/null 2>/dev/null | grep -q "Verify return code: 0"; then
  echo "✅ Certificado SSL válido"
else
  echo "⚠️  Certificado SSL con problemas (posible self-signed)"
fi
echo ""

# Test 5: WebSocket path
echo "🔍 Test 5: WebSocket endpoint"
WS_PATH=$(grep '"path":' /usr/local/etc/xray/config.json | head -n1 | cut -d'"' -f4)
if curl -s -I -H "Upgrade: websocket" -H "Connection: Upgrade" http://localhost/${WS_PATH} | grep -q "HTTP"; then
  echo "✅ WebSocket path responde: $WS_PATH"
else
  echo "❌ WebSocket path no responde: $WS_PATH"
fi
echo ""

# Test 6: Firewall básico
echo "🔍 Test 6: Reglas iptables"
RULES_COUNT=$(iptables -L INPUT | grep -c "ACCEPT\|DROP" || echo "0")
if [ "$RULES_COUNT" -gt "5" ]; then
  echo "✅ Firewall configurado ($RULES_COUNT reglas)"
else
  echo "⚠️  Pocas reglas firewall ($RULES_COUNT)"
fi
echo ""

# Test 7: DNS resolución
echo "🔍 Test 7: Resolución DNS"
MY_IP=$(curl -s --max-time 3 https://ipv4.icanhazip.com)
DNS_IPS=$(dig +short ${DOMAIN} | tr '\n' ' ')
if echo "$DNS_IPS" | grep -q "$MY_IP"; then
  echo "✅ DNS apunta a esta IP: $MY_IP"
else
  echo "⚠️  DNS no incluye esta IP. DNS: $DNS_IPS, Esta IP: $MY_IP"
fi
echo ""

# Test 8: Logs recientes
echo "🔍 Test 8: Logs sin errores críticos"
XRAY_ERRORS=$(journalctl -u xray --since "10 minutes ago" | grep -ci "error\|fail" || echo "0")
NGINX_ERRORS=$(journalctl -u nginx --since "10 minutes ago" | grep -ci "error\|fail" || echo "0")
if [ "$XRAY_ERRORS" -eq "0" ] && [ "$NGINX_ERRORS" -eq "0" ]; then
  echo "✅ Sin errores recientes en logs"
else
  echo "⚠️  Errores en logs: Xray($XRAY_ERRORS), Nginx($NGINX_ERRORS)"
fi
echo ""

# Resumen final
echo "=================== RESUMEN ==================="
if [ -f /root/xray_client_info.txt ]; then
  echo "📱 URLs de cliente:"
  grep "vless://" /root/xray_client_info.txt | head -n3
else
  echo "❌ Archivo de cliente no encontrado"
fi
echo ""

echo "🎯 Prueba manual desde cliente:"
echo "1. Importa URL VLESS en V2RayN/Nekoray"
echo "2. Conecta y navega a cualquier sitio"
echo "3. Verifica IP: https://whatismyipaddress.com"
echo ""

echo "📊 Para monitoreo continuo:"
echo "tail -f /var/log/xray/error.log"
echo "ss -an | grep -E ':80|:443|:2052'"
echo ""

echo "=== TEST COMPLETADO ==="
