# Guía de Instalación y Pruebas - Xray Multi-Edge

## 🎯 Instalación Paso a Paso

### 1. <PERSON><PERSON><PERSON> ORIGIN_PROXY (***************) - Principal oculto
```bash
# Conectarse al VPS origin
ssh root@***************

# Descargar e instalar
curl -sL https://raw.githubusercontent.com/tuusuario/repo/main/origin_proxy.sh | bash

# Verificar instalación
ss -lnpt | grep 53000
systemctl status xray
```

### 2. <PERSON><PERSON><PERSON> EDGE (públicos)
```bash
# En cada VPS edge (************, *************, etc.)
ssh root@IP_EDGE

# IMPORTANTE: Antes de ejecutar, edita edge.sh y ajusta:
# - XRAY_TUNNEL_PATH con el path exacto que generó origin_proxy
# - Verifica que ORIGIN_IP sea ***************

curl -sL https://raw.githubusercontent.com/tuusuario/repo/main/edge.sh | bash

# Verificar DNS apunta al edge
dig +short cdn.rgstreamy.site

# Verificar certificado
openssl s_client -connect cdn.rgstreamy.site:443 -servername cdn.rgstreamy.site < /dev/null
```

## 🔍 Pruebas de Funcionamiento

### Prueba 1: Túnel EDGE → ORIGIN
```bash
# En edge
curl -I http://localhost:18081/
# Debe devolver respuesta del origin (200 o 301)

# Verificar conexión túnel
ss -an | grep 53000
```

### Prueba 2: RTMP y puertos extra
```bash
# Desde cliente externo
telnet IP_EDGE 8880   # RTMP
telnet IP_EDGE 31210  # Puerto extra
```

### Prueba 3: Cliente VLESS
```bash
# Obtener URLs
cat /root/xray_client_info.txt

# Importar en V2RayN/Nekoray/etc:
# vless://<EMAIL>:443?encryption=none&security=tls&type=ws&host=cdn.rgstreamy.site&path=/ws#...
```

## 🛠️ Troubleshooting

### Origin_proxy no escucha en puerto túnel
```bash
systemctl restart xray
journalctl -u xray -f
```

### Edge no puede conectar a origin
```bash
# Verificar firewall origin permite edge
iptables -L INPUT -n | grep 53000

# Test conectividad desde edge
nc -zv *************** 53000
```

### Certificado Let's Encrypt falla
```bash
# Verificar DNS
dig +short cdn.rgstreamy.site

# Forzar renovación
certbot renew --force-renewal
systemctl reload nginx
```

### Cliente no conecta
```bash
# Verificar logs
tail -f /var/log/xray/error.log
tail -f /var/log/nginx/error.log

# Test WebSocket path
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
  http://cdn.rgstreamy.site/ws
```

## 📊 Monitoreo

### Verificar servicios activos
```bash
systemctl status xray nginx fail2ban
ss -lnpt | grep -E "80|443|2052|8880|31210|53000"
```

### Ver estadísticas conexiones
```bash
# Conexiones activas
ss -an | grep -E ":80|:443|:2052"

# Logs fail2ban
fail2ban-client status
fail2ban-client status sshd
```

### Performance red
```bash
# BBR habilitado
sysctl net.ipv4.tcp_congestion_control

# Conexiones concurrentes
ss -s
```

## 🔐 Seguridad

### Verificar firewall
```bash
# Origin_proxy: solo edges pueden acceder
iptables -L INPUT -n --line-numbers

# Edge: puertos públicos protegidos
iptables -L INPUT -n | grep -E "80|443|2052"
```

### Rotar UUID (si necesario)
```bash
# Generar nuevo
NEW_UUID=$(cat /proc/sys/kernel/random/uuid)
echo "Nuevo UUID: $NEW_UUID"

# Actualizar en origin_proxy y todos los edges
# Luego actualizar clientes
```

## 📝 Archivos importantes

- Config Xray: `/usr/local/etc/xray/config.json`
- Config Nginx: `/etc/nginx/sites-available/cdn.rgstreamy.site.conf`
- Logs Xray: `/var/log/xray/error.log`
- Logs Nginx: `/var/log/nginx/error.log`
- Resumen cliente: `/root/xray_client_info.txt`
- Resumen origin: `/root/origin_proxy_info.txt`
