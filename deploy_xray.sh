#!/usr/bin/env bash
# ---------------------------------------------------------------------------------
# Script unificado: Install Xray + Nginx reverse proxy + SSL + Fail2Ban + iptables
# Dominio multi-edge: cdn.rgstreamy.site (puede tener hasta 6 IPs apuntando al mismo FQDN)
# IP origin (main/oculto): *************** 
# Puertos expuestos a clientes: 80 (HTTP WS), 2052 (HTTP WS alterno), 443 (TLS WS)
# Seguridad: iptables endurecido + Fail2Ban (sshd & nginx básicos)
# Certificado: Let's Encrypt (HTTP-01). Fallback: autosigned (auto-renovable vía cron si LE falla).
# Uso: bash deploy_xray.sh
# Probado para Debian/Ubuntu (systemd). Ejecutar como root.
# ---------------------------------------------------------------------------------
set -euo pipefail
IFS=$'\n\t'

# ===================== CONFIGURACION AJUSTABLE =====================
DOMAIN="cdn.rgstreamy.site"      # Dominio compartido por los edges
MODE="edge"                     # edge | origin | origin_proxy
# origin: solo firewall/fail2ban
# origin_proxy: servidor origen con inbound VLESS WS (túnel) sin nginx público

# Lista de IPs públicas de los VPS EDGE (usadas cuando MODE=origin para permitir solo estas)
EDGE_IPS=(
  "************"
  "*************"
  "***********"
  "*************"
  "************"
  "***********"
)

XRAY_WS_PATH="/ws"                # Camino WebSocket clientes
RANDOMIZE_WS_PATH=false            # true => genera path aleatorio una vez (edge) si XRAY_WS_PATH es default
STEALTH_REDUCE_LOGS=true           # Minimiza logs de nginx y xray
APPLY_SYSCTL_TUNING=true           # Ajustes de red para conexiones IPTV
XRAY_INTERNAL_PORT=10000          # Puerto interno (loopback) para WebSocket clientes
XRAY_UUID=""                     # UUID compartido (edge y origin_proxy). Si vacío se genera.

# --------- Parámetros túnel EDGE -> ORIGIN (origin_proxy) ---------
ORIGIN_IP=""                     # IP del origin_proxy (en edges). En origin_proxy se autodetecta si vacío.
TUNNEL_PORT=53000                 # Puerto VLESS WS en origin_proxy
XRAY_TUNNEL_PATH="/xt"           # Path WebSocket túnel
RANDOMIZE_TUNNEL_PATH=false       # Randomizar path túnel si default
EDGE_HTTP_PROXY_PORT=18081        # Puerto loopback en edge para HTTP hacia origin
BACKEND_PORT_HTTP=80              # Backend HTTP origen
BACKEND_PORT_HTTP_ALT=2052        # Backend HTTP alterno (si aplica)
BACKEND_PORT_RTMP=8880            # Backend RTMP
BACKEND_PORT_EXTRA=31210          # Puerto extra
BACKEND_PORT_PANEL=443            # Panel HTTPS
EMAIL_LE="admin@${DOMAIN}"      # Correo para Let's Encrypt
ENABLE_WILDCARD=false             # true requiere desafío DNS manual/automatizado (no implementado por defecto)
USE_ACME_SH=false                 # Si quieres acme.sh (ajusta a true y añade hook DNS si aplicable)
SERVER_IP_OVERRIDE=""            # Forzar IP pública si se deja vacío se autodetecta (edge)
# ===================================================================

# Colores simples
C0="\033[0m"; C1="\033[1;32m"; C2="\033[1;31m"; C3="\033[1;33m"; C4="\033[1;34m"
log(){ echo -e "${C1}[INFO]${C0} $*"; }
warn(){ echo -e "${C3}[WARN]${C0} $*"; }
err(){ echo -e "${C2}[ERROR]${C0} $*" >&2; }

require_root(){ if [[ $(id -u) -ne 0 ]]; then err "Ejecuta como root"; exit 1; fi; }

check_os(){
  if ! command -v apt-get >/dev/null 2>&1; then
    err "Este script esta preparado para Debian/Ubuntu (apt-get)."; exit 1;
  fi
}

ensure_uuid(){
  if [[ -z "$XRAY_UUID" ]]; then
    XRAY_UUID=$(cat /proc/sys/kernel/random/uuid)
  fi
}

detect_public_ip(){
  if [[ -n "$SERVER_IP_OVERRIDE" ]]; then
    SERVER_IP="$SERVER_IP_OVERRIDE"
    return
  fi
  local ip=""
  ip=$(curl -4 -s --max-time 5 https://ipv4.icanhazip.com || true)
  ip=${ip//[$'\r'\n\t '']}  # trim
  if [[ -z "$ip" ]]; then
    ip=$(dig +short A $(hostname -f) 2>/dev/null | head -n1 || true)
  fi
  SERVER_IP="$ip"
}

install_packages(){
  log "Instalando paquetes base..."
  apt-get update -y
  # dnsutils para 'dig'. iptables-persistent trae netfilter-persistent.
  apt-get install -y curl wget unzip tar socat cron iptables-persistent fail2ban nginx jq sed grep openssl ufw certbot dnsutils
}

install_xray(){
  [[ "$MODE" == "origin" ]] && return 0
  if command -v xray >/dev/null 2>&1; then
    log "Xray ya instalado: $(xray -version 2>/dev/null | head -n1)"
    return
  fi
  log "Instalando Xray (core oficial)"
  TMP_DIR=$(mktemp -d)
  cd "$TMP_DIR"
  XRAY_LATEST=$(curl -s https://api.github.com/repos/XTLS/Xray-core/releases/latest | jq -r '.tag_name')
  ARCH=$(uname -m)
  case "$ARCH" in
    x86_64|amd64) XARCH=64;;
    aarch64|arm64) XARCH=arm64-v8a;;
    armv7l) XARCH=arm32-v7a;;
    *) err "Arquitectura no soportada: $ARCH"; exit 1;;
  esac
  FNAME="Xray-linux-$XARCH.zip"
  curl -L -o "$FNAME" "https://github.com/XTLS/Xray-core/releases/download/${XRAY_LATEST}/${FNAME}"
  unzip -q "$FNAME" -d xray
  install -m 755 xray/xray /usr/local/bin/xray
  install -m 755 xray/geosite.dat /usr/local/share/xray/geosite.dat || true
  install -m 755 xray/geoip.dat /usr/local/share/xray/geoip.dat || true
  mkdir -p /usr/local/etc/xray /var/log/xray
  rm -rf "$TMP_DIR"

  cat >/etc/systemd/system/xray.service <<EOF
[Unit]
Description=Xray Core Service
After=network.target nss-lookup.target

[Service]
User=nobody
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE
NoNewPrivileges=true
ExecStart=/usr/local/bin/xray -config /usr/local/etc/xray/config.json
Restart=on-failure
LimitNPROC=512
LimitNOFILE=100000

[Install]
WantedBy=multi-user.target
EOF
}

configure_xray(){
  case "$MODE" in
    edge)
      if $RANDOMIZE_WS_PATH && [[ "$XRAY_WS_PATH" == "/ws" ]]; then
        XRAY_WS_PATH="/"$(tr -dc a-z0-9 </dev/urandom | head -c 6)"/"$(tr -dc a-z0-9 </dev/urandom | head -c 6)
        log "Path WS aleatorio clientes: $XRAY_WS_PATH"
      fi
      if $RANDOMIZE_TUNNEL_PATH && [[ "$XRAY_TUNNEL_PATH" == "/xt" ]]; then
        XRAY_TUNNEL_PATH="/"$(tr -dc a-z0-9 </dev/urandom | head -c 8)
        log "Path túnel aleatorio: $XRAY_TUNNEL_PATH"
      fi
      if [[ -z "$ORIGIN_IP" ]]; then
        warn "ORIGIN_IP no definido: túnel deshabilitado (solo tráfico cliente local)."
      fi
      log "Configurando Xray EDGE (cliente + túnel)"
      cat >/usr/local/etc/xray/config.json <<EOF
{
  "log": {"access": "${STEALTH_REDUCE_LOGS:+/dev/null}", "error": "/var/log/xray/error.log", "loglevel": "warning"},
  "inbounds": [
    {"port": ${XRAY_INTERNAL_PORT}, "listen": "127.0.0.1", "protocol": "vless", "settings": {"clients": [{"id": "${XRAY_UUID}", "level":0, "email": "edge@${DOMAIN}"}], "decryption": "none"}, "streamSettings": {"network":"ws","wsSettings": {"path": "${XRAY_WS_PATH}"}}},
    {"port": 8880, "listen": "0.0.0.0", "protocol": "dokodemo-door", "settings": {"address": "${ORIGIN_IP}", "port": ${BACKEND_PORT_RTMP}, "network": "tcp,udp"}, "tag": "rtmp_in"},
    {"port": 31210, "listen": "0.0.0.0", "protocol": "dokodemo-door", "settings": {"address": "${ORIGIN_IP}", "port": ${BACKEND_PORT_EXTRA}, "network": "tcp,udp"}, "tag": "extra_in"},
    {"port": ${EDGE_HTTP_PROXY_PORT}, "listen": "127.0.0.1", "protocol": "dokodemo-door", "settings": {"address": "${ORIGIN_IP}", "port": ${BACKEND_PORT_HTTP}, "network": "tcp"}, "tag": "http_in"}
  ],
  "outbounds": [
    {"protocol": "freedom", "tag": "direct"},
    {"protocol": "blackhole", "tag": "blocked"}$( [[ -n "$ORIGIN_IP" ]] && echo ",\n    { \"protocol\": \"vless\", \"tag\": \"tunnel\", \"settings\": { \"vnext\": [{ \"address\": \"${ORIGIN_IP}\", \"port\": ${TUNNEL_PORT}, \"users\": [{\"id\": \"${XRAY_UUID}\", \"encryption\": \"none\"}] }] }, \"streamSettings\": { \"network\": \"ws\", \"wsSettings\": { \"path\": \"${XRAY_TUNNEL_PATH}\" } } }" )
  ],
  "routing": {"rules": [ {"type": "field", "inboundTag": ["rtmp_in","extra_in","http_in"], "outboundTag": "tunnel"} ]}
}
EOF
      ;;
    origin_proxy)
      if [[ -z "$ORIGIN_IP" ]]; then
        ORIGIN_IP=$(curl -4 -s --max-time 5 https://ipv4.icanhazip.com || true)
        ORIGIN_IP=${ORIGIN_IP//[$'\r'\n\t ']}
      fi
      if $RANDOMIZE_TUNNEL_PATH && [[ "$XRAY_TUNNEL_PATH" == "/xt" ]]; then
        XRAY_TUNNEL_PATH="/"$(tr -dc a-z0-9 </dev/urandom | head -c 8)
        log "Path túnel aleatorio origin_proxy: $XRAY_TUNNEL_PATH"
      fi
      log "Configurando Xray ORIGIN_PROXY (inbound túnel)"
      cat >/usr/local/etc/xray/config.json <<EOF
{
  "log": {"access": "${STEALTH_REDUCE_LOGS:+/dev/null}", "error": "/var/log/xray/error.log", "loglevel": "warning"},
  "inbounds": [ {"port": ${TUNNEL_PORT}, "listen": "0.0.0.0", "protocol": "vless", "settings": {"clients": [{"id": "${XRAY_UUID}", "level":0, "email": "tunnel@${DOMAIN}"}], "decryption": "none"}, "streamSettings": {"network": "ws", "wsSettings": {"path": "${XRAY_TUNNEL_PATH}"}} } ],
  "outbounds": [ {"protocol": "freedom", "tag": "direct"}, {"protocol": "blackhole", "tag": "blocked"} ],
  "routing": {"rules": []}
}
EOF
      ;;
    origin)
      return 0 ;;
  esac
  systemctl daemon-reload
  systemctl enable --now xray
}

configure_nginx(){
  [[ "$MODE" != "edge" ]] && return 0
  log "Configurando Nginx reverse proxy para Xray (sin cache)"
  mkdir -p /var/www/${DOMAIN}/html
  echo "OK $(date)" >/var/www/${DOMAIN}/html/index.html

  local CERT_EXISTS="no"
  if [[ -f /etc/letsencrypt/live/${DOMAIN}/fullchain.pem && -f /etc/letsencrypt/live/${DOMAIN}/privkey.pem ]]; then
    CERT_EXISTS="si"
  fi

  {
    # HTTP + 2052
  echo "server {"
    echo "    listen 80;"
    echo "    listen 2052;"
    echo "    server_name ${DOMAIN};"
    echo "    root /var/www/${DOMAIN}/html;"
    echo "    index index.html;"
    echo "    add_header Cache-Control 'no-store, no-cache, must-revalidate, max-age=0' always;"
    echo "    add_header Pragma 'no-cache' always;"
    echo "    add_header Expires '0' always;"
  echo "    server_tokens off;"
  echo "    access_log ${STEALTH_REDUCE_LOGS:+off};"
  echo "    keepalive_timeout 15;"
    echo ""
    echo "    location ^~ /.well-known/acme-challenge/ { allow all; }"
    echo ""
    echo "    location ${XRAY_WS_PATH} {"
    echo "        proxy_redirect off;"
    echo "        proxy_pass http://127.0.0.1:${XRAY_INTERNAL_PORT};"
    echo "        proxy_http_version 1.1;"
    echo "        proxy_set_header Upgrade $http_upgrade;"
    echo "        proxy_set_header Connection 'upgrade';"
    echo "        proxy_set_header Host $host;"
    echo "        proxy_set_header X-Real-IP $remote_addr;"
    echo "        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;"
    echo "        add_header Cache-Control 'no-store' always;"
  echo "        proxy_buffering off;"
  echo "        proxy_read_timeout 300;"
    echo "    }"
    echo ""
  echo "    location / {"
  echo "        proxy_pass http://127.0.0.1:${EDGE_HTTP_PROXY_PORT};"
  echo "        proxy_set_header Host $host;"
  echo "        proxy_set_header X-Real-IP $remote_addr;"
  echo "        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;"
  echo "        proxy_set_header X-Forwarded-Proto $scheme;"
  echo "        add_header Cache-Control 'no-store, no-cache, must-revalidate, max-age=0' always;"
  echo "        add_header Pragma 'no-cache' always;"
  echo "        add_header Expires '0' always;"
  echo "    }"
    echo "}"

    if [[ $CERT_EXISTS == "si" ]]; then
      echo ""
      echo "server {"
      echo "    listen 443 ssl http2;"
      echo "    server_name ${DOMAIN};"
      echo "    root /var/www/${DOMAIN}/html;"
  echo "    index index.html;"
      echo "    add_header Cache-Control 'no-store, no-cache, must-revalidate, max-age=0' always;"
      echo "    add_header Pragma 'no-cache' always;"
      echo "    add_header Expires '0' always;"
  echo "    server_tokens off;"
  echo "    access_log ${STEALTH_REDUCE_LOGS:+off};"
  echo "    keepalive_timeout 15;"
      echo ""
      echo "    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;"
      echo "    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem;"
      echo "    ssl_protocols TLSv1.2 TLSv1.3;"
      echo "    ssl_ciphers HIGH:!aNULL:!MD5;"
      echo "    ssl_prefer_server_ciphers on;"
      echo ""
      echo "    location ${XRAY_WS_PATH} {"
      echo "        proxy_redirect off;"
      echo "        proxy_pass http://127.0.0.1:${XRAY_INTERNAL_PORT};"
      echo "        proxy_http_version 1.1;"
      echo "        proxy_set_header Upgrade $http_upgrade;"
      echo "        proxy_set_header Connection 'upgrade';"
      echo "        proxy_set_header Host $host;"
      echo "        proxy_set_header X-Real-IP $remote_addr;"
      echo "        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;"
  echo "        add_header Cache-Control 'no-store' always;"
  echo "        proxy_buffering off;"
  echo "        proxy_read_timeout 300;"
      echo "    }"
      echo ""
  echo "    location / {"
  echo "        proxy_pass http://127.0.0.1:${EDGE_HTTP_PROXY_PORT};"
  echo "        proxy_set_header Host $host;"
  echo "        proxy_set_header X-Real-IP $remote_addr;"
  echo "        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;"
  echo "        proxy_set_header X-Forwarded-Proto $scheme;"
  echo "        add_header Cache-Control 'no-store, no-cache, must-revalidate, max-age=0' always;"
  echo "        add_header Pragma 'no-cache' always;"
  echo "        add_header Expires '0' always;"
  echo "    }"
      echo "}"
    else
      warn "Certificado no presente aún. Bloque 443 diferido."
    fi
  } >/etc/nginx/sites-available/${DOMAIN}.conf

  ln -sf /etc/nginx/sites-available/${DOMAIN}.conf /etc/nginx/sites-enabled/${DOMAIN}.conf
  nginx -t && systemctl reload nginx || { err "Error en configuración Nginx"; exit 1; }
}

issue_ssl(){
  [[ "$MODE" != "edge" ]] && return 0
  log "Intentando emisión de certificado Let's Encrypt (HTTP-01) para ${DOMAIN}"
  # Verificamos que el dominio resuelva a esta IP (mejor esfuerzo)
  DOMAIN_IPS=$(dig +short A ${DOMAIN} 2>/dev/null | sort -u || true)
  if [[ -z "$DOMAIN_IPS" ]]; then warn "No se pudieron resolver A records para ${DOMAIN}."; fi
  if ! grep -q "$SERVER_IP" <<<"$DOMAIN_IPS"; then
    warn "La IP ${SERVER_IP} no aparece todavía en DNS para ${DOMAIN}. Se usará fallback autosigned."
    generate_selfsigned
    return
  fi

  systemctl stop nginx || true
  # Modo webroot usando temporal nginx minimal
  mkdir -p /var/www/${DOMAIN}/html/.well-known/acme-challenge
  systemctl start nginx
  if certbot certonly --agree-tos -m "$EMAIL_LE" --no-eff-email --webroot -w /var/www/${DOMAIN}/html -d ${DOMAIN} -n --rsa-key-size 4096; then
    log "Certificado LE emitido"
  else
    warn "Fallo emisión LE. Usando self-signed"
    generate_selfsigned
  fi
}

generate_selfsigned(){
  [[ "$MODE" != "edge" ]] && return 0
  log "Generando certificado autosigned para ${DOMAIN} (fallback)"
  mkdir -p /etc/letsencrypt/live/${DOMAIN}
  openssl req -x509 -nodes -newkey rsa:4096 -keyout /etc/letsencrypt/live/${DOMAIN}/privkey.pem -out /etc/letsencrypt/live/${DOMAIN}/fullchain.pem -days 90 -subj "/CN=${DOMAIN}" -sha256
  # Cron para auto-regenerar cada 60 días si sigue siendo self-signed
  (crontab -l 2>/dev/null; echo "5 3 */60 * * bash /root/regen_selfsigned_${DOMAIN}.sh >/dev/null 2>&1") | crontab -
  cat >/root/regen_selfsigned_${DOMAIN}.sh <<EOF
#!/usr/bin/env bash
if ! [ -f /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ]; then exit 0; fi
if openssl x509 -in /etc/letsencrypt/live/${DOMAIN}/fullchain.pem -noout -issuer | grep -qi "Let's Encrypt"; then exit 0; fi
openssl req -x509 -nodes -newkey rsa:4096 -keyout /etc/letsencrypt/live/${DOMAIN}/privkey.pem -out /etc/letsencrypt/live/${DOMAIN}/fullchain.pem -days 90 -subj "/CN=${DOMAIN}" -sha256
systemctl reload nginx || true
EOF
  chmod +x /root/regen_selfsigned_${DOMAIN}.sh
}

configure_fail2ban(){
  log "Configurando Fail2Ban"
  # jail.local básico
  cat >/etc/fail2ban/jail.local <<'EOF'
[DEFAULT]
bantime = 1h
findtime = 10m
maxretry = 5
backend = systemd
ignoreip = 127.0.0.1/8

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 4

[nginx-404]
enabled = true
port = http,https
filter = nginx-404
logpath = /var/log/nginx/access.log
maxretry = 15
findtime = 10m
bantime = 30m
EOF
  # Filtro 404
  cat >/etc/fail2ban/filter.d/nginx-404.conf <<'EOF'
[Definition]
failregex = ^<HOST> - - .*"(GET|POST).+" 404 .*
ignoreregex =
EOF
  systemctl enable --now fail2ban
  systemctl restart fail2ban
}

configure_iptables(){
  log "Aplicando reglas iptables endurecidas (MODE=${MODE})"
  # Limpieza
  iptables -F
  iptables -X
  iptables -t nat -F
  iptables -t mangle -F
  iptables -t raw -F

  # Politicas por defecto
  iptables -P INPUT DROP
  iptables -P FORWARD DROP
  iptables -P OUTPUT ACCEPT

  # Loopback y establecido
  iptables -A INPUT -i lo -j ACCEPT
  iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

  # ICMP limitado
  iptables -A INPUT -p icmp -m limit --limit 4/s --limit-burst 20 -j ACCEPT

  if [[ "$MODE" == "edge" ]]; then
    # SSH (22) con protección básica (tasa SYN)
    iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW -m limit --limit 30/min --limit-burst 20 -j ACCEPT
    iptables -A INPUT -p tcp --dport 22 -j DROP

    # Puertos servicio: 80,443,2052 - rate limit SYN
    for p in 80 443 2052; do
      iptables -A INPUT -p tcp --dport $p -m conntrack --ctstate NEW -m limit --limit 300/second --limit-burst 2000 -j ACCEPT
      iptables -A INPUT -p tcp --dport $p -m conntrack --ctstate NEW -m hashlimit --hashlimit-name connperip$p --hashlimit 30/second --hashlimit-burst 120 --hashlimit-mode srcip --hashlimit-upto 30/second -j ACCEPT
      # Fallback aceptar (balance entre protección y disponibilidad)
      iptables -A INPUT -p tcp --dport $p -m conntrack --ctstate NEW -j ACCEPT
    done
  elif [[ "$MODE" == "origin" ]]; then
    # Solo permitir HTTP(S) y SSH desde EDGE_IPS
    for ip in "${EDGE_IPS[@]}"; do
      iptables -A INPUT -p tcp -s "$ip" --dport 22 -j ACCEPT
      for p in 80 443 2052; do iptables -A INPUT -p tcp -s "$ip" --dport $p -m conntrack --ctstate NEW -j ACCEPT; done
    done
    iptables -A INPUT -p tcp --dport 22 -j DROP
    for p in 80 443 2052; do iptables -A INPUT -p tcp --dport $p -j DROP; done
  elif [[ "$MODE" == "origin_proxy" ]]; then
    # Permitir túnel y SSH solo desde EDGE_IPS
    for ip in "${EDGE_IPS[@]}"; do
      iptables -A INPUT -p tcp -s "$ip" --dport 22 -j ACCEPT
      iptables -A INPUT -p tcp -s "$ip" --dport ${TUNNEL_PORT} -m conntrack --ctstate NEW -j ACCEPT
    done
    iptables -A INPUT -p tcp --dport 22 -j DROP
    iptables -A INPUT -p tcp --dport ${TUNNEL_PORT} -j DROP
    # Bloquear accesos externos directos a puertos backend (80,443,2052,8880,31210) si quieres ocultarlos
    for p in 80 443 2052 8880 31210; do iptables -A INPUT -p tcp --dport $p -j DROP; done
  fi

  # Bloquear scans simples (XMAS, NULL)
  iptables -A INPUT -p tcp --tcp-flags ALL NONE -j DROP
  iptables -A INPUT -p tcp --tcp-flags ALL ALL -j DROP

  # Guardar
  netfilter-persistent save || bash -c 'iptables-save > /etc/iptables/rules.v4'
}

create_client_summary(){
  if [[ "$MODE" == "edge" ]]; then
    log "Generando resumen de acceso (edge)"
    local VLESS_TLS="vless://${XRAY_UUID}@${DOMAIN}:443?encryption=none&security=tls&type=ws&host=${DOMAIN}&path=${XRAY_WS_PATH}#${DOMAIN}-TLS"
    local VLESS_80="vless://${XRAY_UUID}@${DOMAIN}:80?encryption=none&type=ws&host=${DOMAIN}&path=${XRAY_WS_PATH}#${DOMAIN}-80"
    local VLESS_2052="vless://${XRAY_UUID}@${DOMAIN}:2052?encryption=none&type=ws&host=${DOMAIN}&path=${XRAY_WS_PATH}#${DOMAIN}-2052"
    cat >/root/xray_client_info.txt <<EOF
================ Xray Client Info (EDGE) ================
Dominio: ${DOMAIN}
UUID: ${XRAY_UUID}
Path WebSocket clientes: ${XRAY_WS_PATH}
IP Edge (detectada): ${SERVER_IP}

Túnel -> ORIGIN_IP: ${ORIGIN_IP:-NO_CONFIG}  (port ${TUNNEL_PORT}, path ${XRAY_TUNNEL_PATH})
Servicios expuestos vía túnel:
 - HTTP origin ${BACKEND_PORT_HTTP} via edge nginx 80/2052/443
 - RTMP ${BACKEND_PORT_RTMP} via edge:8880
 - EXTRA ${BACKEND_PORT_EXTRA} via edge:31210

URLs VLESS clientes:
 TLS (443):  ${VLESS_TLS}
 80 (HTTP):  ${VLESS_80}
 2052 (HTTP): ${VLESS_2052}

Archivo Nginx: /etc/nginx/sites-available/${DOMAIN}.conf
Config Xray:  /usr/local/etc/xray/config.json
Logs Xray:    /var/log/xray/
=================================================
EOF
    echo -e "${C4}"; cat /root/xray_client_info.txt; echo -e "${C0}"
  elif [[ "$MODE" == "origin" ]]; then
    log "Resumen modo ORIGIN"
    cat >/root/origin_firewall_info.txt <<EOF
================ Origin Firewall Info ================
Rol: ORIGIN (no expone Xray)
IPs Edge permitidas:
$(printf ' - %s\n' "${EDGE_IPS[@]}")
Puertos permitidos a Edge: 22 (SSH), 80, 2052, 443
Acceso externo a otros puertos: bloqueado
Archivo script: $(realpath "$0")
=====================================================
EOF
    echo -e "${C4}"; cat /root/origin_firewall_info.txt; echo -e "${C0}"
  elif [[ "$MODE" == "origin_proxy" ]]; then
    log "Resumen modo ORIGIN_PROXY"
    cat >/root/origin_proxy_info.txt <<EOF
================ Origin Proxy Info ================
Rol: ORIGIN_PROXY (recibe túnel VLESS desde edges)
IP Pública Origen: ${ORIGIN_IP}
Puerto túnel: ${TUNNEL_PORT}
Path túnel: ${XRAY_TUNNEL_PATH}
UUID compartido: ${XRAY_UUID}
Servicios backend locales:
 - HTTP: ${BACKEND_PORT_HTTP}
 - HTTP_ALT: ${BACKEND_PORT_HTTP_ALT}
 - RTMP: ${BACKEND_PORT_RTMP}
 - EXTRA: ${BACKEND_PORT_EXTRA}
 - PANEL HTTPS: ${BACKEND_PORT_PANEL}
Archivo config Xray: /usr/local/etc/xray/config.json
==================================================
EOF
    echo -e "${C4}"; cat /root/origin_proxy_info.txt; echo -e "${C0}"
  fi
}

main(){
  require_root
  check_os
  detect_public_ip
  ensure_uuid
  install_packages
  configure_fail2ban
  if $APPLY_SYSCTL_TUNING; then sysctl_tuning || true; fi

  case "$MODE" in
    edge)
      install_xray
      configure_xray
      configure_nginx
      issue_ssl
      systemctl enable --now certbot.timer 2>/dev/null || true
      configure_nginx
      systemctl reload nginx || true
      ;;
    origin_proxy)
      install_xray
      configure_xray
      ;;
    origin)
      ;; # solo firewall/fail2ban
    *) err "MODE desconocido: $MODE"; exit 1;;
  esac

  configure_iptables
  create_client_summary
  log "Proceso completado (MODE=${MODE})."
  case "$MODE" in
    edge)
      warn "Si usaste fallback self-signed, reintenta emitir LE (certbot renew) cuando DNS apunte correctamente.";;
    origin)
      warn "Origin protegido: no apuntes el dominio aquí.";;
    origin_proxy)
      warn "Origin_proxy listo: apunta SOLO edges al dominio público.";;
  esac
}

main "$@"

# Función añadida al final para claridad
sysctl_tuning(){
  log "Aplicando sysctl tuning (red)"  
  cat >/etc/sysctl.d/99-xray-tuning.conf <<'EOF'
net.core.somaxconn = 4096
net.core.netdev_max_backlog = 4096
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_max_syn_backlog = 4096
net.ipv4.ip_local_port_range = 20000 65000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fastopen = 3
net.ipv4.tcp_mtu_probing = 1
net.ipv4.tcp_congestion_control = bbr
EOF
  sysctl --system >/dev/null 2>&1 || sysctl -p /etc/sysctl.d/99-xray-tuning.conf || true
}
