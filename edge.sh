#!/usr/bin/env bash
# Instalación automática EDGE (servidores públicos)
# Ejecutar: curl -sL https://raw.githubusercontent.com/tuusuario/repo/main/edge.sh | bash

set -euo pipefail

# Configuración para EDGE
export DOMAIN="cdn.rgstreamy.site"
export MODE="edge"
export ORIGIN_IP="***************"  # IP del origin_proxy
export XRAY_UUID="a1b2c3d4-e5f6-7890-1234-567890abcdef"  # EL MISMO UUID del origin_proxy
export TUNNEL_PORT=53000
export XRAY_TUNNEL_PATH="/xt"  # Usar el path exacto que generó origin_proxy
export RANDOMIZE_WS_PATH=true
export STEALTH_REDUCE_LOGS=true
export APPLY_SYSCTL_TUNING=true

echo "=== INSTALANDO EDGE ==="
echo "Conectando a origin: $ORIGIN_IP:$TUNNEL_PORT"
echo "UUID: $XRAY_UUID"
echo "Dominio: $DOMAIN"

# Descargar y ejecutar script principal
curl -sL "https://raw.githubusercontent.com/tuusuario/repo/main/deploy_xray.sh" > /tmp/deploy_xray.sh
chmod +x /tmp/deploy_xray.sh
bash /tmp/deploy_xray.sh

echo ""
echo "=== EDGE INSTALADO ==="
echo "URLs para clientes:"
cat /root/xray_client_info.txt | grep -A3 "URLs VLESS"
echo ""
echo "Pruebas rápidas:"
echo "1. Certificado: openssl s_client -connect $DOMAIN:443 -servername $DOMAIN < /dev/null"
echo "2. Túnel HTTP: curl -I http://localhost:18081/"
echo "3. Logs: tail -f /var/log/xray/error.log"
