#!/usr/bin/env bash
# Instalación automática ORIGIN_PROXY (servidor principal oculto)
# IP: ***************
# Ejecutar: curl -sL https://raw.githubusercontent.com/tuusuario/repo/main/origin_proxy.sh | bash

set -euo pipefail

# Configuración para ORIGIN_PROXY (***************)
export DOMAIN="cdn.rgstreamy.site"
export MODE="origin_proxy"
export XRAY_UUID="a1b2c3d4-e5f6-7890-1234-567890abcdef"  # Cambiar por uno único
export TUNNEL_PORT=53000
export XRAY_TUNNEL_PATH="/xt"
export RANDOMIZE_TUNNEL_PATH=true
export STEALTH_REDUCE_LOGS=true
export APPLY_SYSCTL_TUNING=true

# IPs de los VPS EDGE que pueden conectarse
export EDGE_IPS=(
  "************"
  "*************"
  "***********"
  "*************"
  "************"
  "***********"
)

echo "=== INSTALANDO ORIGIN_PROXY (***************) ==="
echo "UUID: $XRAY_UUID"
echo "Puerto túnel: $TUNNEL_PORT"
echo "Permitiendo solo edges: ${EDGE_IPS[*]}"

# Descargar y ejecutar script principal
curl -sL "https://raw.githubusercontent.com/tuusuario/repo/main/deploy_xray.sh" > /tmp/deploy_xray.sh
chmod +x /tmp/deploy_xray.sh
bash /tmp/deploy_xray.sh

echo ""
echo "=== ORIGIN_PROXY INSTALADO ==="
echo "Anota estos datos para configurar los EDGES:"
echo "ORIGIN_IP: $(curl -s https://ipv4.icanhazip.com)"
echo "UUID: $XRAY_UUID"
echo "TUNNEL_PORT: $TUNNEL_PORT"
echo "TUNNEL_PATH: $(grep '"path":' /usr/local/etc/xray/config.json | head -n1 | cut -d'"' -f4)"
echo ""
echo "Verifica que está funcionando:"
echo "ss -lnpt | grep $TUNNEL_PORT"
echo "systemctl status xray"
